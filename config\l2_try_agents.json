{"id": "l2_try_agents", "version": "1.0", "pipeline": [{"step": "SpeechToText", "agent": "stt_agent", "input": {"audio": "user_input_audio"}, "tools": {"external_tools": "stt"}, "output": {"trans": "trans"}}, {"step": "FillerTTS", "agent": "preprocessing_agent", "input": {"trans": "trans"}, "tools": {"external_tools": "openai"}, "output": {"filler_text": "filler_text"}}, {"step": "IntentDetection", "agent": "filler_tts_agent", "input": {"filler_text": "filler_text"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "filler_text": "filler_text"}}, {"step": "TTS_Response", "agent": "tts_agent", "input": {"filler_text": "filler_text"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "IntentDetection"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}