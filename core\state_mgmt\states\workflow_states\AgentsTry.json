{"workflow": {"id": "generic_banking_workflow", "name": "Generic Banking Support Flow", "version": "1.0", "start": "state_greeting", "states": {"state_greeting": {"id": "state_greeting", "type": "input", "layer2_id": "l2_try_agents", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'check_balance'", "target": "state_check_balance"}, {"condition": "intent == 'loan_inquiry'", "target": "state_loan_inquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}}}}